"""
Story Generation Routes for Socket Service V2

Collection-based story generation that returns story_set_id instead of individual items.
"""

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from app.shared.security import get_tenant_info
from app.v2.api.socket_service_v2.generator.story_generator_v2 import generate_story_collection
from typing import Dict, Any

logger = setup_new_logging(__name__)

router = APIRouter(
    prefix="/story",
    tags=["Story Generation V2"],
)


@router.post("/generate")
async def generate_story_v2(
    audio_file: UploadFile = File(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Generate story collection from audio (V2).
    
    This V2 endpoint:
    1. Processes audio to generate stories
    2. Creates a story collection with collection ID
    3. Returns story_set_id instead of individual story items
    4. Supports background processing for better performance
    
    Args:
        audio_file: Audio file upload
        current_user: Current authenticated user
        
    Returns:
        Dictionary with story_set_id and collection metadata
    """
    try:
        audio_data = await audio_file.read()
        logger.info(f"Processing audio file for story V2: {audio_file.filename}, size: {len(audio_data)} bytes")

        # Get story prompt from database or use default
        story_prompt_doc = await current_user.async_db.prompts.find_one({"name": "story_prompt"})
        if story_prompt_doc:
            prompt = story_prompt_doc.get("prompt", DEFAULT_STORY_PROMPT)
        else:
            prompt = DEFAULT_STORY_PROMPT

        # Process audio with V2 story generator
        result = await generate_story_collection(
            audio_data,
            prompt,
            current_user
        )

        if not result.get("story_set_id"):
            raise HTTPException(
                status_code=400,
                detail="Failed to generate story collection from audio"
            )

        logger.info(f"✅ Generated story collection V2: {result['story_set_id']}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating story V2: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate story collection")


@router.get("/collection/{story_set_id}")
async def get_story_collection(
    story_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Get story collection by ID.
    
    Args:
        story_set_id: Story collection identifier
        current_user: Current authenticated user
        
    Returns:
        Story collection data with metadata
    """
    try:
        # Get story collection
        collection = await current_user.async_db.story_collections.find_one(
            {"collection_id": story_set_id, "user_id": str(current_user.user.id)}
        )
        
        if not collection:
            raise HTTPException(status_code=404, detail="Story collection not found")

        # Get story sets in the collection
        story_sets = []
        for story_set_id in collection.get("story_set_ids", []):
            story_set = await current_user.async_db.story_steps.find_one(
                {"_id": story_set_id}
            )
            if story_set:
                story_sets.append({
                    "story_set_id": str(story_set["_id"]),
                    "steps": story_set.get("steps", []),
                    "total_steps": story_set.get("total_steps", 0),
                    "completed_steps": story_set.get("completed_steps", 0),
                    "status": story_set.get("status", "unknown"),
                    "created_at": story_set.get("created_at"),
                    "updated_at": story_set.get("updated_at")
                })

        return {
            "collection_id": collection["collection_id"],
            "user_id": collection["user_id"],
            "session_id": collection.get("session_id"),
            "total_story_sets": collection.get("total_story_sets", 0),
            "total_stories": collection.get("total_stories", 0),
            "total_steps": collection.get("total_steps", 0),
            "completed_steps": collection.get("completed_steps", 0),
            "status": collection.get("status"),
            "created_at": collection.get("created_at"),
            "updated_at": collection.get("updated_at"),
            "completed_at": collection.get("completed_at"),
            "story_sets": story_sets,
            "generation_metadata": collection.get("generation_metadata", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting story collection: {e}")
        raise HTTPException(status_code=500, detail="Failed to get story collection")


@router.get("/collection/{story_set_id}/step/{step_number}")
async def get_story_step(
    story_set_id: str,
    step_number: int,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> Dict[str, Any]:
    """
    Get a specific step from a story collection.
    
    Args:
        story_set_id: Story collection identifier
        step_number: Step number (1-based)
        current_user: Current authenticated user
        
    Returns:
        Specific story step data
    """
    try:
        # Get story collection
        collection = await current_user.async_db.story_collections.find_one(
            {"collection_id": story_set_id, "user_id": str(current_user.user.id)}
        )
        
        if not collection:
            raise HTTPException(status_code=404, detail="Story collection not found")

        # Get the first story set (for now, collections contain one story set)
        if not collection.get("story_set_ids"):
            raise HTTPException(status_code=404, detail="No story sets in collection")

        story_set = await current_user.async_db.story_steps.find_one(
            {"_id": collection["story_set_ids"][0]}
        )
        
        if not story_set:
            raise HTTPException(status_code=404, detail="Story set not found")

        steps = story_set.get("steps", [])
        
        # Find the requested step (step_number is 1-based)
        requested_step = None
        for step in steps:
            if step.get("stage") == step_number:
                requested_step = step
                break

        if not requested_step:
            raise HTTPException(status_code=404, detail=f"Step {step_number} not found")

        return {
            "collection_id": story_set_id,
            "story_set_id": str(story_set["_id"]),
            "step": requested_step,
            "total_steps": story_set.get("total_steps", 0),
            "completed_steps": story_set.get("completed_steps", 0),
            "status": story_set.get("status"),
            "created_at": story_set.get("created_at"),
            "updated_at": story_set.get("updated_at")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting story step: {e}")
        raise HTTPException(status_code=500, detail="Failed to get story step")


# Default story prompt (same as V1 but optimized for collection-based storage)
DEFAULT_STORY_PROMPT = """You are a skilled Nepali children's story writer, creating heartwarming, culturally rich tales for ages 6-10, inspired by Nepali folklore. Translate the *essence* of an audio clip (description provided) into an *original* Nepali children's story, formatted as a JSON object with 5 stages. Each stage includes:

*   **Nepali Script:** A compelling narrative (3-5 sentences) starting with a traditional Nepali opening (e.g., Ekadesh ma...). Use simple language, vivid descriptions, and convey emotions through actions. Incorporate Nepali culture and maintain a positive tone.
*   **English Image Prompt:** *A brief, one-sentence description of the scene, including the setting, main characters, and overall mood.* (e.g., \"A young girl smiles as she waters plants in a sunny village garden.\")

**Key Requirements (Concise):**

*   **5 Stages:** Exactly five stages in the story.
*   **Age 6-10:** Vocabulary and themes appropriate for the target audience.
*   **Nepali Culture:** Deeply rooted in Nepali traditions (family, festivals, nature, crafts). Avoid stereotypes.
*   **Emotional Resonance:** Capture the core emotions of the audio clip.
*   **Authentic Voice:** Natural, engaging Nepali script.
*   **Clear Images:** Brief, one-sentence image prompts.
*   **Creative Storytelling:** Memorable characters, enchanting settings, and engaging plot.

The story must be formatted as a JSON object, meticulously structured as follows:

```json
{
  \"Story Steps\": [
    {
      \"stage\": 1,
      \"script\": \"[Compelling Nepali script for stage 1. This should be a short paragraph (3-5 sentences) that introduces the setting, characters, and the initial situation.  Use vivid descriptions and evocative language to capture the reader's attention. Write in simple, age-appropriate Nepali. Focus on showing, not telling. mostly begin with ekadesh ma or ek din ko kura ho or ek samaya ko kura ho ]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 1.  Describe the scene, including the setting, main characters, and overall mood.]\"
    },
    {
      \"stage\": 2,
      \"script\": \"[Compelling Nepali script for stage 2. Continue the story, introducing a challenge, conflict, or new development. Maintain the simple language and vivid descriptions.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 2.]\"
    },
    {
      \"stage\": 3,
      \"script\": \"[Compelling Nepali script for stage 3. This stage should represent a turning point or a moment of decision for the characters.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 3.]\"
    },
    {
      \"stage\": 4,
      \"script\": \"[Compelling Nepali script for stage 4.  Show the consequences of the characters' actions or decisions.  Build towards the resolution.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 4.]\"
    },
    {
      \"stage\": 5,
      \"script\": \"[Compelling Nepali script for stage 5.  Provide a satisfying resolution to the story.  Leave the reader with a positive message or a sense of hope.  The ending should be heartwarming and memorable.]\",
      \"image\": \"[Brief, one-sentence English image prompt for stage 5.]\"
    }
  ]
}
```"""
