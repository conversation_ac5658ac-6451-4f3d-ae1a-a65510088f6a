"""
Task Utilities V2 for Socket Service V2

Collection-based task and story utilities that handle saving to collections
and returning collection IDs instead of individual items.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId
import asyncio
import uuid

from app.shared.db_enums import TaskStatus, QuizType, InputType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.prompt_maker_v2 import generate as generate_v2
from app.v2.api.socket_service_v2.generator.story_generator_v2 import generate_story_collection

# Configure logging
logger = setup_new_logging(__name__)

# Retry configuration
MAX_RETRIES = 3
INITIAL_RETRY_DELAY = 1.0
MAX_RETRY_DELAY = 30.0


async def retry_with_exponential_backoff(func, *args, **kwargs):
    """Retry function with exponential backoff."""
    delay = INITIAL_RETRY_DELAY
    
    for attempt in range(MAX_RETRIES):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == MAX_RETRIES - 1:
                raise e
            
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_RETRY_DELAY)


async def save_task_collection_and_items(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Optional[Any] = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Save tasks using existing task_sets collection and return collection ID.

    This V2 version saves to existing task_sets collection but returns a collection ID
    that can reference multiple task_sets if needed.

    Args:
        current_user: Current user context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker_v2.py
        collection_id: Optional existing collection ID, creates new one if None
        audio_storage_info: Optional MinIO storage information
        socketio_server: Optional SocketIO server for real-time updates
        use_background_tasks: Whether to use background processing

    Returns:
        Dictionary with task_set_id (collection ID) and metadata
    """
    try:
        if not tasks_data or not tasks_data.get("tasks"):
            return {
                "status": "error",
                "error": "No tasks data provided",
                "task_set_id": None
            }

        # Generate collection ID if not provided (this will be the task_set_id)
        if not collection_id:
            collection_id = str(uuid.uuid4())

        tasks = tasks_data["tasks"]
        optimization_stats = tasks_data.get("optimization_stats", {})
        usage_metadata = tasks_data.get("usage_metadata", {})
        title = tasks_data.get("title", "Generated Task Set V2")

        logger.info(f"💾 Saving task set V2 {collection_id} with {len(tasks)} tasks")

        # Create task set document using existing task_sets collection structure
        task_set_id = ObjectId()
        task_set_doc = {
            "_id": task_set_id,
            "user_id": str(current_user.user.id),
            "session_id": session_id,
            "title": title,
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated below
            "total_tasks": len(tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": TaskStatus.PENDING,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "optimization_metadata": optimization_stats,
            "usage_metadata": usage_metadata,
            "service_version": "v2"
        }

        # Process and add tasks to the set
        total_score = 0
        processed_tasks = []
        
        for task_data in tasks:
            try:
                # Create task item
                task_item = {
                    "id": ObjectId(),
                    "type": QuizType(task_data.get("type", "single_choice")),
                    "title": task_data.get("title", "Generated Task"),
                    "question": task_data.get("question", {}),
                    "correct_answer": {
                        "text": task_data.get("question", {}).get("answer", ""),
                        "value": task_data.get("question", {}).get("answer", "")
                    },
                    "user_answer": None,
                    "story": task_data.get("story"),
                    "status": TaskStatus.PENDING,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False)
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                
                processed_tasks.append(task_item)
                total_score += task_item["total_score"]
                
            except Exception as e:
                logger.error(f"Error processing task: {e}")
                continue

        # Update task set with processed tasks
        task_set_doc["tasks"] = processed_tasks
        task_set_doc["total_score"] = total_score

        # Save task set to existing task_sets collection
        await current_user.async_db.task_sets.insert_one(task_set_doc)

        logger.info(f"✅ Saved task set V2 {collection_id} with task set {task_set_id}")

        return {
            "status": "success",
            "task_set_id": collection_id,  # Return collection ID (same as v2_collection_id)
            "collection_metadata": {
                "total_task_sets": 1,
                "total_tasks": len(processed_tasks),
                "optimization_stats": optimization_stats,
                "actual_task_set_id": str(task_set_id),
                "service_version": "v2"
            },
            "tasks": processed_tasks  # For compatibility
        }

    except Exception as e:
        logger.error(f"❌ Error saving task collection: {e}")
        return {
            "status": "error",
            "error": str(e),
            "task_set_id": None
        }


async def _create_or_update_task_collection(
    current_user: UserTenantDB,
    collection_id: str,
    task_set_id: str,
    session_id: str,
    optimization_stats: Dict[str, Any],
    usage_metadata: Dict[str, Any],
    audio_storage_info: Optional[Dict[str, Any]] = None
):
    """Create or update task collection document."""
    try:
        # Check if collection already exists
        existing_collection = await current_user.async_db.task_collections.find_one(
            {"collection_id": collection_id}
        )

        if existing_collection:
            # Update existing collection
            await current_user.async_db.task_collections.update_one(
                {"collection_id": collection_id},
                {
                    "$push": {"task_set_ids": task_set_id},
                    "$inc": {
                        "total_task_sets": 1,
                        "total_tasks": optimization_stats.get("total_tasks", 0)
                    },
                    "$set": {
                        "updated_at": datetime.now(timezone.utc),
                        "optimization_metadata": optimization_stats,
                        "usage_metadata": usage_metadata
                    }
                }
            )
            logger.info(f"Updated existing task collection {collection_id}")
        else:
            # Create new collection
            collection_doc = {
                "_id": ObjectId(),
                "collection_id": collection_id,
                "user_id": str(current_user.user.id),
                "session_id": session_id,
                "task_set_ids": [task_set_id],
                "input_type": InputType.AUDIO,
                "input_metadata": audio_storage_info,
                "total_task_sets": 1,
                "total_tasks": optimization_stats.get("total_tasks", 0),
                "status": TaskStatus.PENDING,
                "created_at": datetime.now(timezone.utc),
                "optimized_for_performance": True,
                "media_excluded_count": optimization_stats.get("media_excluded_count", 0),
                "optimization_metadata": optimization_stats,
                "generation_metadata": {
                    "usage_metadata": usage_metadata,
                    "version": "v2"
                }
            }
            
            await current_user.async_db.task_collections.insert_one(collection_doc)
            logger.info(f"Created new task collection {collection_id}")

    except Exception as e:
        logger.error(f"Error creating/updating task collection: {e}")
        raise


async def process_audio_with_prompt_maker_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4
) -> Dict[str, Any]:
    """
    Process audio with the optimized V2 prompt maker.
    
    Args:
        current_user: Current user context
        audio_bytes: Audio data to process
        num_tasks: Number of tasks to generate
        
    Returns:
        Parsed tasks data from prompt_maker_v2.py
    """
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker V2")

        # Call the V2 prompt maker with retry logic
        result = await retry_with_exponential_backoff(
            generate_v2,
            audio_bytes,
            num_tasks,
            current_user
        )
        
        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker_v2.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "optimization_stats": {},
                "usage_metadata": {}
            }

        logger.info(f"✅ Generated {len(result['tasks'])} tasks with V2 optimizations")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with prompt_maker V2: {e}")
        return {
            "tasks": [],
            "error": str(e),
            "status": "error",
            "optimization_stats": {},
            "usage_metadata": {}
        }


async def process_audio_with_story_generator_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    prompt: str
) -> Dict[str, Any]:
    """
    Process audio with the V2 story generator.
    
    Args:
        current_user: Current user context
        audio_bytes: Audio data to process
        prompt: Story generation prompt
        
    Returns:
        Story collection result with story_set_id
    """
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with story_generator V2")

        # Call the V2 story generator with retry logic
        result = await retry_with_exponential_backoff(
            generate_story_collection,
            audio_bytes,
            prompt,
            current_user
        )
        
        if not result or not result.get("story_set_id"):
            error_msg = "No story collection ID returned from story_generator_v2"
            logger.error(error_msg)
            return {
                "story_set_id": None,
                "error": error_msg,
                "status": "error"
            }

        logger.info(f"✅ Generated story collection: {result['story_set_id']}")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with story_generator V2: {e}")
        return {
            "story_set_id": None,
            "error": str(e),
            "status": "error"
        }


def convert_to_socketio_format_v2(tasks_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert V2 tasks data to Socket.IO format.
    
    This maintains compatibility with existing Socket.IO clients while
    including V2 optimization metadata.
    """
    try:
        tasks = tasks_data.get("tasks", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        
        socketio_tasks = []
        for task in tasks:
            socketio_task = {
                "id": str(task.get("id", ObjectId())),
                "type": task.get("type", "single_choice"),
                "title": task.get("title", "Generated Task"),
                "question": task.get("question", {}),
                "total_score": task.get("total_score", 10),
                "difficulty_level": task.get("difficulty_level", 2),
                "status": task.get("status", "pending"),
                # V2 specific fields
                "_v2_optimized": task.get("_v2_optimized", False),
                "_media_excluded": task.get("_media_excluded", False)
            }
            
            # Add story if present and not optimized away
            if task.get("story") and not task.get("_media_excluded", False):
                socketio_task["story"] = task["story"]
            
            socketio_tasks.append(socketio_task)
        
        logger.info(f"Converted {len(socketio_tasks)} tasks to Socket.IO format with V2 optimizations")
        return socketio_tasks
        
    except Exception as e:
        logger.error(f"Error converting tasks to Socket.IO format: {e}")
        return []
