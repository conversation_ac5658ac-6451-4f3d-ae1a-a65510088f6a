"""
Socket Service V2 - Collection-Based Real-time Socket.IO Communication

This service handles ONLY Socket.IO real-time communication with V2 enhancements:
- Socket.IO authentication via /connect endpoint (V2 optimized)
- WebSocket connection management (same as V1)
- Real-time audio streaming with performance optimizations
- Collection-based response format (task_set_id, story_set_id)
- Session management with V2 metadata

Key V2 Features:
- Same Socket.IO communication flow as original service
- Collection-based storage using existing task_sets and story_steps
- Performance optimized task generation (no media for choice questions)
- Returns collection IDs instead of individual items
- Maintains compatibility with existing client implementations
- Uses existing shared SocketIO server (app.shared.socketio)

Architecture:
- FastAPI application with Socket.IO integration
- Redis-backed session management and queue processing
- Uses existing MongoDB collections (task_sets, story_steps)
- Background audio processing with Gemini AI
- Performance optimizations for faster processing

All other functionality has been moved to the Management Service.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.socketio_server import SocketIOServer
from app.shared.redis.redis_manager import RedisManager
from app.shared.redis.queue.queue_worker import QueueWorker

# Import routes
from app.v2.api.socket_service_v2.routes.socket_auth import router as socket_auth_router
from app.v2.api.socket_service_v2.routes.audio import router as audio_router
from app.v2.api.socket_service_v2.routes.story import router as story_router

# Configure logging
logger = setup_new_logging(__name__)

# Global instances
redis_manager = None
socketio_server = None
queue_worker = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown."""
    global redis_manager, socketio_server, queue_worker

    try:
        logger.info("🚀 Starting Socket Service V2...")

        # Initialize Redis
        import os
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_manager = RedisManager(redis_url=redis_url)
        await redis_manager.ensure_connected()
        logger.info("✅ Redis connection established")

        # Initialize Socket.IO server (using existing shared server)
        socketio_server = SocketIOServer(redis_manager)
        await socketio_server.setup()
        logger.info("✅ Socket.IO server initialized")

        # Initialize Queue Worker (using existing shared worker)
        queue_worker = QueueWorker(
            audio_queue_manager=socketio_server.audio_queue_manager,
            socketio_server=socketio_server
        )

        # Start queue worker in background
        import asyncio
        asyncio.create_task(queue_worker.start())
        logger.info("✅ Queue worker started")

        # Mount Socket.IO app at root since Traefik will handle the /v2 prefix
        app.mount("/socket.io", socketio_server.app)
        logger.info("✅ Socket.IO app mounted at /socket.io")

        # Log startup completion message
        logger.info("🚀 Socket Service V2 started successfully!")
        logger.info("📡 Collection-based Socket.IO communication ready")
        logger.info("⚡ Performance optimizations enabled")
        logger.info("🔄 Using existing task_sets and story_steps collections")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to start Socket Service V2: {e}")
        raise
    finally:
        logger.info("🛑 Shutting down Socket Service V2...")
        
        # Stop queue worker
        if queue_worker:
            await queue_worker.stop()
            logger.info("✅ Queue worker stopped")
        
        # Close Redis connections
        if redis_manager:
            await redis_manager.close()
            logger.info("✅ Redis connections closed")


# Create FastAPI instance
app = FastAPI(
    title="Nepali App - Socket Service V2",
    description="Collection-based real-time Socket.IO communication service for audio streaming and task generation",
    version="2.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v2/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    servers=[
        {
            "url": "/v2",
            "description": "Socket Service V2 API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add file size limit middleware for audio uploads
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse

class FileSizeLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, max_upload_size: int = 200 * 1024 * 1024):  # 200MB default
        super().__init__(app)
        self.max_upload_size = max_upload_size

    async def dispatch(self, request: Request, call_next):
        # Only check file size for audio upload endpoints
        if request.url.path.endswith("/audio/process") and request.method == "POST":
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > self.max_upload_size:
                return JSONResponse(
                    status_code=413,
                    content={
                        "detail": f"File too large. Maximum size allowed: {self.max_upload_size // (1024*1024)}MB"
                    }
                )

        response = await call_next(request)
        return response

# Import max file size from config
from app.shared.config import MAX_AUDIO_FILE_SIZE
app.add_middleware(FileSizeLimitMiddleware, max_upload_size=MAX_AUDIO_FILE_SIZE)

# Include routers
app.include_router(socket_auth_router, tags=["Socket.IO Authentication V2"])
app.include_router(audio_router, tags=["Audio Processing V2"])
app.include_router(story_router, tags=["Story Generation V2"])


# Custom OpenAPI schema generation to ensure correct server URLs
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/v2",
            "description": "Socket Service V2 API"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint for Socket Service V2.

    Returns basic service information and available endpoints.
    """
    return {
        "service": "Nepali App - Socket Service V2",
        "version": "2.0.0",
        "description": "Collection-based real-time Socket.IO communication service",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "Collection-based task and story storage",
            "Performance optimized task generation (no media for choice questions)",
            "Same Socket.IO communication flow as V1",
            "Returns task_set_id and story_set_id instead of individual items",
            "Uses existing task_sets and story_steps collections",
            "Background audio processing with Gemini AI"
        ],
        "endpoints": {
            "socket_auth": "/v2/connect - Create authenticated Socket.IO session",
            "websocket": "/v2/socket.io - WebSocket connection endpoint",
            "audio_processing": "/v2/audio/process - HTTP audio processing (alternative to WebSocket)",
            "story_generation": "/v2/story/generate - HTTP story generation",
            "health": "/v2/health - Service health check",
            "docs": "/v2/docs - API documentation"
        },
        "socket_flow": {
            "1": "POST /v2/connect to get session_token",
            "2": "Connect to WebSocket at /v2/socket.io with session_token",
            "3": "Send 'stream_starting' event",
            "4": "Stream binary audio data",
            "5": "Send 'stream_completed' event",
            "6": "Receive task_set_id and story_set_id"
        },
        "v2_optimizations": {
            "media_exclusion": "Choice questions exclude images/audio for performance",
            "collection_based": "Returns collection IDs instead of individual items",
            "existing_collections": "Uses existing task_sets and story_steps collections"
        }
    }


# Custom docs endpoints (same as V1 but for V2)
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom Swagger UI for Socket Service V2."""
    from fastapi.openapi.docs import get_swagger_ui_html
    return get_swagger_ui_html(
        openapi_url="/v2/openapi.json",
        title="Socket Service V2 API",
        swagger_favicon_url="/static/favicon.ico",
        oauth2_redirect_url="/v2/docs/oauth2-redirect",
    )


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """Custom ReDoc for Socket Service V2."""
    from fastapi.openapi.docs import get_redoc_html
    return get_redoc_html(
        openapi_url="/v2/openapi.json",
        title="Socket Service V2 API",
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/openapi.json", include_in_schema=False)
async def get_openapi_schema():
    """Get OpenAPI schema for Socket Service V2."""
    return app.openapi()


@app.get("/health")
async def health_check():
    """
    Health check endpoint for Socket Service V2.

    Returns the health status of all service components including:
    - Redis connection
    - Queue worker status
    - Socket.IO server status
    - Database connectivity (if applicable)
    """
    global redis_manager, socketio_server, queue_worker

    health_status = {
        "service": "Nepali App - Socket Service V2",
        "version": "2.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {},
        "v2_features": {
            "collection_based_storage": True,
            "performance_optimized": True,
            "media_optimization": True,
            "existing_collections": True
        }
    }

    # Check Redis connection
    if redis_manager:
        try:
            await redis_manager.ping()
            health_status["components"]["redis"] = {
                "status": "healthy",
                "message": "Redis connection active"
            }
        except Exception as e:
            health_status["components"]["redis"] = {
                "status": "unhealthy",
                "message": f"Redis connection failed: {str(e)}"
            }
            health_status["status"] = "degraded"
    else:
        health_status["components"]["redis"] = {
            "status": "not_initialized",
            "message": "Redis manager not initialized"
        }
        health_status["status"] = "degraded"

    # Check Socket.IO server
    if socketio_server:
        health_status["components"]["socketio"] = {
            "status": "healthy",
            "message": "Socket.IO server initialized"
        }
    else:
        health_status["components"]["socketio"] = {
            "status": "not_initialized",
            "message": "Socket.IO server not initialized"
        }
        health_status["status"] = "degraded"

    # Check Queue Worker
    if queue_worker:
        try:
            worker_health = await queue_worker.health_check()
            health_status["components"]["queue_worker"] = {
                "status": "healthy" if worker_health.get("is_running", False) else "degraded",
                "details": worker_health
            }
            if not worker_health.get("is_running", False):
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["queue_worker"] = {
                "status": "unhealthy",
                "message": f"Queue worker check failed: {str(e)}"
            }
            health_status["status"] = "degraded"
    else:
        health_status["components"]["queue_worker"] = {
            "status": "not_initialized",
            "message": "Queue worker not initialized"
        }
        health_status["status"] = "degraded"

    return health_status


@app.get("/health/redis")
async def redis_health():
    """Redis health check endpoint for V2."""
    try:
        if redis_manager:
            await redis_manager.ping()
            return {"status": "healthy", "redis": "connected", "service_version": "v2"}
        return {"status": "unhealthy", "redis": "not_initialized", "service_version": "v2"}
    except Exception as e:
        return {"status": "unhealthy", "redis": f"error: {str(e)}", "service_version": "v2"}


@app.get("/health/queue")
async def queue_health():
    """Queue system health check endpoint for V2."""
    try:
        if queue_worker:
            health_status = await queue_worker.health_check()
            return {"status": "healthy", "queue": health_status, "service_version": "v2"}
        return {"status": "unhealthy", "queue": "not_initialized", "service_version": "v2"}
    except Exception as e:
        return {"status": "unhealthy", "queue": f"error: {str(e)}", "service_version": "v2"}


@app.get("/health/detailed")
async def detailed_health():
    """Detailed health check for all components in V2."""
    try:
        health_data = {
            "service": "socket_service_v2",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }

        # Redis health
        try:
            if redis_manager:
                await redis_manager.ping()
                health_data["components"]["redis"] = {"status": "healthy"}
            else:
                health_data["components"]["redis"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["redis"] = {"status": "unhealthy", "error": str(e)}

        # Queue health
        try:
            if queue_worker:
                queue_health_data = await queue_worker.health_check()
                health_data["components"]["queue"] = {"status": "healthy", "details": queue_health_data}
            else:
                health_data["components"]["queue"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["queue"] = {"status": "unhealthy", "error": str(e)}

        # SocketIO health
        try:
            if socketio_server:
                health_data["components"]["socketio"] = {"status": "healthy"}
            else:
                health_data["components"]["socketio"] = {"status": "not_initialized"}
        except Exception as e:
            health_data["components"]["socketio"] = {"status": "unhealthy", "error": str(e)}

        # Overall status
        all_healthy = all(
            comp.get("status") == "healthy"
            for comp in health_data["components"].values()
        )
        health_data["status"] = "healthy" if all_healthy else "unhealthy"

        return health_data

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "socket_service_v2",
            "version": "2.0.0",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/status")
async def service_status():
    """
    Get detailed service status and statistics for Socket Service V2.

    Returns information about active sessions, processing statistics,
    and V2-specific optimizations.
    """
    return {
        "service": "Socket Service V2",
        "version": "2.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "features": {
            "collection_based_storage": True,
            "performance_optimized": True,
            "media_optimization": True,
            "existing_collections_reuse": True,
            "socket_communication": "same_as_v1"
        },
        "optimizations": {
            "choice_questions": "media_excluded_for_performance",
            "interactive_tasks": "media_included_for_functionality",
            "storage": "existing_task_sets_and_story_steps",
            "response_format": "collection_ids_instead_of_individual_items"
        },
        "endpoints": {
            "socket_auth": "/connect",
            "websocket": "/socket.io",
            "health": "/health",
            "status": "/status",
            "docs": "/docs"
        }
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for Socket Service V2."""
    logger.error(f"Unhandled exception in Socket Service V2: {str(exc)}")
    logger.error(f"Request URL: {request.url}")
    return JSONResponse(
        status_code=500,
        content={
            "service": "Socket Service V2",
            "error": "Internal server error",
            "message": "An unexpected error occurred in the Socket Service V2",
            "timestamp": datetime.now().isoformat()
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        log_level="info",
        access_log=True
    )
